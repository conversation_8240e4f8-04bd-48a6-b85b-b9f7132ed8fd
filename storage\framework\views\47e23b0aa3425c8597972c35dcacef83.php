<div>
    <!-- Video Modal -->
    <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 <?php if(!$isOpen): ?> hidden <?php endif; ?>">
        <div class="relative max-w-4xl w-full p-4">
            <button wire:click="closeModal" class="absolute -top-2 -right-2 text-white bg-gray-700 hover:bg-gray-600 rounded-full p-2 z-10">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <video id="videoModalContent" controls class="w-full max-h-[85vh] rounded-lg shadow-lg" preload="metadata" src="<?php echo e($videoUrl); ?>"></video>
            <p id="videoModalTitle" class="text-center text-white text-sm mt-2"><?php echo e($title); ?></p>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:initialized', function() {
            // Reset video when modal is closed
            Livewire.hook('element.updated', ({ el, component }) => {
                if (component.name === 'video-modal') {
                    const videoElement = document.getElementById('videoModalContent');
                    if (videoElement && !<?php echo \Illuminate\Support\Js::from($isOpen)->toHtml() ?>) {
                        videoElement.pause();
                    }
                }
            });
        });
    </script>
</div>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\minewache-website\resources\views/livewire/video-modal.blade.php ENDPATH**/ ?>