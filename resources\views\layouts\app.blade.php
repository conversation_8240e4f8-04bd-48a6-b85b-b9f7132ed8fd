<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth" data-theme="minewache">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Die offizielle Webseite der Minewache - Die beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum">
    <meta name="theme-color" content="#265FC2">
    <meta name="keywords" content="Die Minewache, Minecraft Polizei Serie, Minecraft Rollenspiel, Minecraft Krimi, Minecraft Verfolgungsjagd, Minecraft Polizeiserie, Polizei Rollenspiel Minecraft, Minecraft Story Serie, Minecraft Großeinsatz">

    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="Die Minewache - {{ $heading ?? 'Minecraft Polizeiserie' }}">
    <meta property="og:description" content="Die offizielle Webseite der Minewache - Die beliebte Minecraft-Polizeiserie mit spannenden Verfolgungsjagden, Kriminalfällen und Rollenspiel im Minecraft-Universum">
    <meta property="og:image" content="{{ asset('og-image.png') }}">

    <!-- Canonical URL -->
    <x-canonical-url />

    <title>Die Minewache - {{ $heading ?? 'Minecraft Polizeiserie' }}</title>

    <!-- Favicons -->
    <link rel="icon" type="image/png" href="{{ asset('favicon-96x96.png') }}" sizes="96x96">
    <link rel="icon" type="image/svg+xml" href="{{ asset('favicon.svg') }}">
    <link rel="shortcut icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('apple-touch-icon.png') }}">
    <meta name="apple-mobile-web-app-title" content="Die Minewache">
    <link rel="manifest" href="{{ asset('site.webmanifest') }}">

    <!-- Preload kritische Ressourcen -->
    <!-- Using Fontsource instead of local fonts -->

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Literata:ital,wght@0,400;0,700;1,400&family=Sora:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Inline theme styles to prevent FOUC -->
    <style>
        /* Dark theme (minewache) */
        [data-theme="minewache"] {
            --color-base-100: #0f172a; /* slate-900 */
            --color-base-100-rgb: 15, 23, 42; /* RGB for base-100 */
            --color-base-200: #1e293b; /* slate-800 */
            --color-base-200-rgb: 30, 41, 59; /* RGB for base-200 */
            --color-base-300: #334155; /* slate-700 */
            --color-base-300-rgb: 51, 65, 85; /* RGB for base-300 */
            --color-base-content: #f8fafc; /* slate-50 */
            --color-base-content-rgb: 248, 250, 252; /* RGB for base-content */
            --color-primary: #3b82f6; /* blue-500 */
            --color-primary-content: #ffffff; /* white */
            --color-primary-rgb: 59, 130, 246; /* RGB for primary */
        }

        /* Light theme (minewache-light) */
        [data-theme="minewache-light"] {
            --color-base-100: #f8fafc; /* slate-50 */
            --color-base-100-rgb: 248, 250, 252; /* RGB for base-100 */
            --color-base-200: #f1f5f9; /* slate-100 */
            --color-base-200-rgb: 241, 245, 249; /* RGB for base-200 */
            --color-base-300: #e2e8f0; /* slate-200 */
            --color-base-300-rgb: 226, 232, 240; /* RGB for base-300 */
            --color-base-content: #0f172a; /* slate-900 */
            --color-base-content-rgb: 15, 23, 42; /* RGB for base-content */
            --color-primary: #3b82f6; /* blue-500 */
            --color-primary-content: #ffffff; /* white */
            --color-primary-rgb: 59, 130, 246; /* RGB for primary */
        }

        /* Auto theme (follows system preference) */
        [data-theme="minewache-auto"] {
            --color-base-100: #0f172a; /* slate-900 */
            --color-base-100-rgb: 15, 23, 42; /* RGB for base-100 */
            --color-base-200: #1e293b; /* slate-800 */
            --color-base-200-rgb: 30, 41, 59; /* RGB for base-200 */
            --color-base-300: #334155; /* slate-700 */
            --color-base-300-rgb: 51, 65, 85; /* RGB for base-300 */
            --color-base-content: #f8fafc; /* slate-50 */
            --color-base-content-rgb: 248, 250, 252; /* RGB for base-content */
            --color-primary: #3b82f6; /* blue-500 */
            --color-primary-content: #ffffff; /* white */
            --color-primary-rgb: 59, 130, 246; /* RGB for primary */
        }

        /* High contrast theme for accessibility */
        [data-theme="minewache-high-contrast"] {
            --color-base-100: #000000;
            --color-base-100-rgb: 0, 0, 0;
            --color-base-200: #1a1a1a;
            --color-base-200-rgb: 26, 26, 26;
            --color-base-300: #333333;
            --color-base-300-rgb: 51, 51, 51;
            --color-base-content: #ffffff;
            --color-base-content-rgb: 255, 255, 255;
            --color-primary: #0066ff;
            --color-primary-content: #ffffff;
            --color-primary-rgb: 0, 102, 255;
        }

        /* Colorful theme for vibrant experience */
        [data-theme="minewache-colorful"] {
            --color-base-100: #0f172a; /* slate-900 */
            --color-base-100-rgb: 15, 23, 42; /* RGB for base-100 */
            --color-base-200: #1e293b; /* slate-800 */
            --color-base-200-rgb: 30, 41, 59; /* RGB for base-200 */
            --color-base-300: #334155; /* slate-700 */
            --color-base-300-rgb: 51, 65, 85; /* RGB for base-300 */
            --color-base-content: #f8fafc; /* slate-50 */
            --color-base-content-rgb: 248, 250, 252; /* RGB for base-content */
            --color-primary: #8b5cf6; /* purple-500 */
            --color-primary-content: #ffffff; /* white */
            --color-primary-rgb: 139, 92, 246; /* RGB for primary */
        }

        /* Glassmorphism effect */
        .glass {
            background: rgba(30, 41, 59, 0.6); /* slate-800 with transparency */
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
        }
    </style>

    <!-- Environment-aware logging utility (load first) -->
    <script src="{{ asset('js/utils/logger.js') }}"></script>

    <!-- Theme initialization script -->
    <script src="{{ asset('js/theme-init.js') }}"></script>

    <!-- WebSocket Fallback Configuration (only in development) -->
    @if(config('debugging.frontend.websocket_debug', false))
    <script src="{{ asset('js/direct-reverb.js') }}"></script>
    @endif

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/css/modern-ui.css', 'resources/js/app.js'])

    <!-- jQuery for AJAX handling -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

    <!-- Discord Membership Handler -->
    <script src="{{ asset('js/discord-membership-handler.js') }}"></script>

    <!-- Discord Redirect Handler -->
    <script src="{{ asset('js/discord-redirect.js') }}"></script>

    <!-- Response Generator Script -->
    <script src="{{ asset('js/response-generator.js') }}"></script>

    <!-- WebSocket Manager Script -->
    <script src="{{ asset('js/websocket-manager.js') }}"></script>
</head>
<body class="font-sans antialiased flex flex-col min-h-screen bg-gradient-modern text-base-content">
<!-- Theme Switcher -->
<x-enhanced-theme-switcher position="bottom-right" type="simple" />
<!-- Skip to content link für Accessibility -->
<a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:p-4 focus:bg-primary focus:text-primary-content">
    {{ __('messages.skip_to_content') }}
</a>

<!-- WebSocket Status Container -->
<div id="websocket-status" class="fixed bottom-4 left-4 z-50"></div>

@if (session('status'))
    <x-modern-card variant="default" size="sm">
        <div class="flex items-center gap-2 sm:gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current text-success flex-shrink-0 h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span class="text-sm sm:text-base">{{ session('status') }}</span>
        </x-modern-card>
    </div>
@endif

<div class="flex-grow flex flex-col">
    @include('layouts.navigation')

    <!-- Page Heading -->
    @if(isset($header) && $header)
        <header class="w-full xl:max-w-[90%] 2xl:max-w-[85%] mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
            <x-modern-card variant="elevated" size="sm">
                {{ $header }}
            </x-modern-card>
        </header>
    @endif

    <!-- Breadcrumbs -->
    @if((isset($breadcrumbs) && $breadcrumbs) || (isset($autoBreadcrumbs) && count($autoBreadcrumbs) > 0))
        <div class="w-full xl:max-w-[90%] 2xl:max-w-[85%] mx-auto px-4 sm:px-6 lg:px-8 mt-3 sm:mt-4">
            <x-modern-card variant="default" size="md">
                @if(isset($breadcrumbs) && $breadcrumbs)
                    {{ $breadcrumbs }}
                @elseif(isset($autoBreadcrumbs) && count($autoBreadcrumbs) > 0)
                    <x-breadcrumbs :items="$autoBreadcrumbs" />
                @endif
            </x-modern-card>
        </div>
    @endif

    <!-- Page Content -->
    <main id="main-content" class="flex-grow px-4 sm:px-6 lg:px-8 mx-auto w-full xl:max-w-[90%] 2xl:max-w-[85%]">
        @yield('content')
        {{ $slot ?? '' }}
    </main>
</div>

<footer class="glass text-base-content py-6 sm:py-8 w-full mt-8 shadow-2xl rounded-t-xl sm:rounded-t-3xl mx-auto w-full xl:max-w-[90%] 2xl:max-w-[85%]">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8">
            <!-- Logo and description -->
            <div class="flex flex-col items-center sm:items-start">
                <div class="flex items-center mb-3">
                    <x-application-logo class="block h-7 sm:h-8 w-auto fill-current text-primary" />
                    <span class="ml-2 font-bold text-base sm:text-lg">Die Minewache</span>
                </div>
                <p class="text-xs sm:text-sm text-base-content/80 text-center sm:text-left mb-3 sm:mb-4 max-w-xs mx-auto sm:mx-0">
                    {{ __('messages.footer_description') }}
                </p>
                <div class="flex space-x-3 sm:space-x-4 mt-1 sm:mt-2">
                    <a href="https://discord.gg/wwrK2csZnX" class="bg-slate-700/50 hover:bg-slate-600/50 text-[#5865F2] hover:text-primary-focus transition-colors p-1.5 sm:p-2 rounded-full" aria-label="Discord">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3847-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                        </svg>
                    </a>
                    <a href="https://www.youtube.com/channel/UCXbp7C3lL8jG8RyRGqzWKUA" class="bg-slate-700/50 hover:bg-slate-600/50 text-[#FF0000] hover:text-primary-focus transition-colors p-1.5 sm:p-2 rounded-full" aria-label="YouTube">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Quick links -->
            <div class="flex flex-col items-center sm:items-start mt-4 sm:mt-0">
                <h3 class="text-base sm:text-lg font-bold mb-2 sm:mb-4">{{ __('messages.quick_links') }}</h3>
                <ul class="space-y-1 sm:space-y-2 w-full">
                    <li><a href="{{ route('home') }}" class="text-base-content hover:text-primary transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-slate-700/30 block w-full text-center sm:text-left text-sm sm:text-base">{{ __('navigation.home') }}</a></li>
                    <li><a href="{{ route('youtube') }}" class="text-base-content hover:text-primary transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-slate-700/30 block w-full text-center sm:text-left text-sm sm:text-base">{{ __('navigation.youtube') }}</a></li>
                    <li><a href="{{ route('mods') }}" class="text-base-content hover:text-primary transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-slate-700/30 block w-full text-center sm:text-left text-sm sm:text-base">{{ __('navigation.mods') }}</a></li>
                    <li><a href="{{ route('bewerben') }}" class="text-base-content hover:text-primary transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-slate-700/30 block w-full text-center sm:text-left text-sm sm:text-base">{{ __('navigation.apply') }}</a></li>
                </ul>
            </div>

            <!-- Legal -->
            <div class="flex flex-col items-center sm:items-start mt-4 sm:mt-0">
                <h3 class="text-base sm:text-lg font-bold mb-2 sm:mb-4">{{ __('messages.legal') }}</h3>
                <ul class="space-y-1 sm:space-y-2 w-full">
                    <li><a href="{{ route('impressum') }}" class="text-base-content hover:text-primary transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-slate-700/30 block w-full text-center sm:text-left text-sm sm:text-base">Impressum</a></li>
                    <li><a href="{{ route('datenschutz') }}" class="text-base-content hover:text-primary transition-colors px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg hover:bg-slate-700/30 block w-full text-center sm:text-left text-sm sm:text-base">Datenschutzerklärung</a></li>
                </ul>
                <div class="mt-4 sm:mt-6 text-xs sm:text-sm text-base-content/70 text-center sm:text-left w-full">
                    &copy; {{ date('Y') }} Die Minewache. {{ __('messages.all_rights_reserved') }}
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Theme is now managed by the theme-switcher component -->
<script>
    // Ensure theme is properly initialized
    document.addEventListener('DOMContentLoaded', () => {
        console.log('Layout: DOMContentLoaded event fired');
        // Check if Alpine.js is loaded and initialized
        if (window.Alpine) {
            console.log('Layout: Alpine.js is available');
        } else {
            console.log('Layout: Alpine.js is not available yet');
        }
    });
</script>

<!-- Cookie Consent Banner -->
<x-cookie-consent position="bottom" />

<!-- GDPR Consent Modal -->
<x-gdpr-consent-modal :show="session()->has('show_gdpr_consent')" />

<!-- Gemini AI Consent Modal -->
@livewire('gemini-consent-modal')

<!-- User Data Consent Modal -->
@livewire('user-data-consent-modal')

@stack('scripts')

<!-- Structured Data -->
@includeWhen(true, 'partials.structured-data')
</body>
</html>
